# TradingView Automation Environment Variables - SIMPLIFIED
# ============================================

# 🔐 SECURITY NOTICE: Never commit this file to version control!

# Environment Configuration
ENVIRONMENT=development

# Database Configuration (Supabase)
SUPABASE_URL="https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"
USE_SUPABASE=true
SUPABASE_CLIENT_TYPE=sdk
SUPABASE_FALLBACK_IP=***********

# Redis Configuration
REDIS_URL="redis://:123SECURE_REDIS_PASSWORD!@#@redis:6379/0"
REDIS_PASSWORD="123SECURE_REDIS_PASSWORD!@#"
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_ENABLED=true

# Security Configuration
JWT_SECRET="e8b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1"

# Discord Configuration
DISCORD_BOT_TOKEN="MTQwNDUwNjk2MTc3NjQ4MDMxNg.G52FIT.2yZMLen9t7P44DMdTMKMilQviklM2L812u-EVw"
DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/1409753017137369088/2wUoyZFHFMs5DbAiBfqd6IvzRauFWc4cjC5AJLPD61D71qh6IXrIaVULTu_HvejxFyyZ"
DISCORD_ENABLED=true
DISCORD_USE_SLASH_COMMANDS=true
DISCORD_MAX_RETRIES=3
DISCORD_GUILD_ID=

# Application Configuration
DEBUG=true
LOG_LEVEL=INFO
SKIP_DB_CONNECTION=false

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Data Provider Configuration
ALPHA_VANTAGE_API_KEY="DDI08KAP03QTQT2B"
FINNHUB_API_KEY="d2mjok1r01qog4441lsgd2mjok1r01qog4441lt0"
POLYGON_API_KEY="********************************"
ALPACA_API_KEY="DEMO_ALPACA_KEY"
YAHOO_FINANCE_ENABLED=true

# Data Processing Configuration
DATA_PROVIDER_TIMEOUT=10
DEFAULT_MARKET_DATA_SOURCE=yahoo_finance
ENABLE_DATA_CACHING=true
DATA_CACHE_TTL=300

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true
PERFORMANCE_MONITORING=true

# 🤖 AI Configuration - SIMPLIFIED (6 variables instead of 20+)
OPENROUTER_API_KEY="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"

# Core AI Models - Only what we actually need
AI_MODEL_QUICK="gpt-4o-mini"                                    # Symbol extraction, intent classification
AI_MODEL_ANALYSIS="anthropic/claude-3.5-sonnet"                # Market analysis, technical analysis  
AI_MODEL_HEAVY="deepcogito/cogito-v2-preview-deepseek-671b"    # Risk assessment, user explanations
AI_MODEL_FALLBACK="gpt-4o-mini"                                # When others fail

# AI Behavior Settings
AI_TIMEOUT_SECONDS="30"
AI_MAX_RETRIES="3"

# Web Search Configuration
WEB_SEARCH_URL="https://api.duckduckgo.com/"
BRAVE_SEARCH_API_KEY="BSATFBoiEbnPS39Gnkx6WFQVz75FuVm"

# API Server Configuration
WEBHOOK_HOST="0.0.0.0"
WEBHOOK_PORT="8001"

# Timeout Configuration
AI_PROCESSING_TIMEOUT="15.0"
BOT_API_TIMEOUT="45.0"
DATA_PROVIDER_TIMEOUT="10.0"
AI_SYMBOL_EXTRACTOR_TIMEOUT="10.0"

# Redis Configuration
REDIS_CONNECT_TIMEOUT="5"
REDIS_SOCKET_TIMEOUT="5"

# Alternative API (optional)
OPENAI_API_KEY=""

# Webhook Configuration
WEBHOOK_SECRET="tradingview_automation_secret_key_2025"

# Direct Postgres Connection (for legacy compatibility)
DATABASE_URL="postgresql+asyncpg://postgres.sgxjackuhalscowqrulv:<EMAIL>:6543/postgres"

# Fallback configuration for when APIs fail
FALLBACK_DATA_SOURCES=["yahoo", "finnhub_free"]
RATE_LIMIT_RETRY_ATTEMPTS=3
RATE_LIMIT_BACKOFF_MULTIPLIER=2
MAX_RATE_LIMIT_WAIT=60
