"""
Data provider aggregator for fallback logic.
Manages multiple data providers with graceful fallback between them.
Uses centralized configuration for provider order and settings.
Updated to use unified base classes for consistency.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .unified_base import UnifiedDataProvider
from .alpha_vantage import AlphaVantageProvider
from .yfinance_provider import YFinanceProvider
from .fallback_provider import FallbackProvider
from src.shared.config import get_config
from src.shared.services.optimization_service import optimization_service

logger = logging.getLogger(__name__)

class DataProviderAggregator:
    """
    Aggregator that manages multiple data providers with fallback logic.
    Tries providers in configured order until one succeeds.
    Uses centralized configuration for settings.
    Updated to use unified base classes for consistency.

    Implements singleton pattern to avoid redundant initializations.
    """

    _instance = None
    _initialized = False

    def __new__(cls, provider_order: Optional[List[str]] = None):
        """Singleton pattern to avoid multiple initializations"""
        if cls._instance is None:
            cls._instance = super(DataProviderAggregator, cls).__new__(cls)
        return cls._instance

    def __init__(self, provider_order: Optional[List[str]] = None):
        """
        Initialize the data provider aggregator.

        Args:
            provider_order: List of provider names in preferred order.
                           If None, uses default order from centralized configuration.
        """
        # Only initialize once
        if self._initialized:
            return

        self.providers: Dict[str, UnifiedDataProvider] = {}
        self.provider_order = provider_order or self._get_default_provider_order()
        self._initialize_providers()
        self._initialized = True

    @classmethod
    def reset_instance(cls):
        """Reset singleton instance (for testing purposes)"""
        cls._instance = None
        cls._initialized = False
    
    def _get_default_provider_order(self) -> List[str]:
        """
        Get default provider order from centralized configuration.

        Returns:
            List of provider names in preferred order
        """
        import os

        # In production, exclude fallback provider by default
        environment = os.getenv('ENVIRONMENT', 'production').lower()
        if environment in ['production', 'prod']:
            default_order = ['yfinance', 'alpha_vantage', 'polygon', 'finnhub']
        else:
            # In development/test, include fallback as last resort
            default_order = ['yfinance', 'alpha_vantage', 'polygon', 'finnhub', 'fallback']

        # Use config.get() with environment-appropriate default provider order
        config_manager = get_config()
        return config_manager.get('data_providers.provider_order', default_order)
    
    def _initialize_providers(self):
        """Initialize all configured providers using centralized configuration"""
        provider_map = {
            "alpha_vantage": AlphaVantageProvider,
            "yfinance": YFinanceProvider,
            "fallback": FallbackProvider
        }
        
        # Add import statements at the top of the file if needed
        try:
            # Import Polygon provider if available
            from .polygon_provider import PolygonProvider
            provider_map["polygon"] = PolygonProvider
        except ImportError:
            logger.warning("Polygon provider module not found, skipping")
            
        try:
            # Import Finnhub provider if available
            from .finnhub_provider import FinnhubProvider
            provider_map["finnhub"] = FinnhubProvider
        except ImportError:
            logger.warning("Finnhub provider module not found, skipping")

        try:
            # Import Alpaca provider if available
            from .alpaca_provider import AlpacaProvider
            provider_map["alpaca"] = AlpacaProvider
        except ImportError:
            logger.warning("Alpaca provider module not found, skipping")
        
        for provider_name in self.provider_order:
            if provider_name in provider_map:
                try:
                    # Check if provider is enabled in configuration
                    config_manager = get_config()
                    provider_config = config_manager.get(f'data_providers.{provider_name}', {})
                    if not provider_config.get('enabled', True):
                        logger.info(f"Provider {provider_name} is disabled in configuration, skipping")
                        continue
                        
                    provider_instance = provider_map[provider_name]()
                    # is_configured may be an attribute (bool) or a callable method; handle both
                    configured_flag = getattr(provider_instance, 'is_configured', True)
                    if callable(configured_flag):
                        configured = bool(configured_flag())
                    else:
                        configured = bool(configured_flag)

                    if configured:
                        self.providers[provider_name] = provider_instance
                        logger.info(f"Initialized data provider: {provider_name}")
                    else:
                        logger.warning(f"Provider {provider_name} is not configured, skipping")
                except Exception as e:
                    logger.error(f"Failed to initialize provider {provider_name}: {e}")
            else:
                logger.warning(f"Unknown provider: {provider_name}")
    
    def get_available_providers(self) -> List[str]:
        """
        Get list of available and configured providers.
        
        Returns:
            List of provider names that are configured and ready
        """
        return list(self.providers.keys())
    
    async def get_ticker(self, symbol: str, preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get ticker data, trying providers in order until one succeeds.
        
        Args:
            symbol: Stock symbol to fetch data for
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            Dictionary with ticker data or error information from last provider
        """
        # Check cache first
        try:
            # Use the global optimization service instance
            cache_key = f"ticker_{symbol}_{preferred_provider or 'default'}"
            cached_result = await optimization_service.cache.get('ticker', {'key': cache_key})
            
            if cached_result and not cached_result.get('error'):
                logger.debug(f"Using cached ticker data for {symbol}")
                return cached_result
        except Exception as e:
            logger.debug(f"Cache check failed for ticker {symbol}: {e}")
        
        providers_to_try = self._get_providers_to_try(preferred_provider)
        
        last_error = None
        for provider_name in providers_to_try:
            provider = self.providers.get(provider_name)
            if not provider:
                continue
                
            try:
                result = await provider.get_ticker(symbol)
                if "error" not in result:
                    logger.debug(f"Successfully fetched {symbol} from {provider_name}")
                    
                    # Cache successful result
                    try:
                        await optimization_service.cache.set('ticker', {'key': cache_key}, result, ttl=60)
                    except Exception as e:
                        logger.debug(f"Failed to cache ticker result for {symbol}: {e}")
                    
                    return result
                else:
                    last_error = result["error"]
                    logger.warning(f"Provider {provider_name} failed for {symbol}: {last_error}")
            except Exception as e:
                last_error = str(e)
                logger.error(f"Provider {provider_name} error for {symbol}: {e}")
        
        # All providers failed
        error_msg = f"All data providers failed for {symbol}"
        if last_error:
            error_msg += f". Last error: {last_error}"
        
        logger.error(error_msg)
        return {"error": error_msg}

    async def get_stock_price(self, symbol: str, preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get stock price (alias for get_ticker for backward compatibility).

        Args:
            symbol: Stock symbol to fetch data for
            preferred_provider: Specific provider to try first (optional)

        Returns:
            Dictionary with stock price data or error information
        """
        return await self.get_ticker(symbol, preferred_provider)

    async def get_current_price(self, symbol: str, preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get current price (alias for get_ticker for backward compatibility).

        Args:
            symbol: Stock symbol to fetch data for
            preferred_provider: Specific provider to try first (optional)

        Returns:
            Dictionary with current price data or error information
        """
        return await self.get_ticker(symbol, preferred_provider)

    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d",
                         preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get historical data, trying providers in order until one succeeds.
        
        Args:
            symbol: Stock symbol to fetch data for
            period: Time period (e.g., "1d", "1mo", "1y")
            interval: Data interval (e.g., "1d", "1h", "15m")
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            Dictionary with historical data or error information from last provider
        """
        providers_to_try = self._get_providers_to_try(preferred_provider)
        
        last_error = None
        for provider_name in providers_to_try:
            provider = self.providers.get(provider_name)
            if not provider:
                continue
                
            try:
                # Check if provider has get_history method
                if hasattr(provider, 'get_history'):
                    result = await provider.get_history(symbol, period, interval)
                # Fall back to get_historical_data method
                elif hasattr(provider, 'get_historical_data'):
                    # Try different parameter combinations based on provider type
                    try:
                        # First try with period and interval (for YFinance)
                        result = await provider.get_historical_data(symbol, period=period, interval=interval)
                    except TypeError:
                        try:
                            # Convert period to days for providers that expect days parameter
                            days = 30  # Default to 30 days
                            if period.endswith('d'):
                                days = int(period[:-1])
                            elif period.endswith('mo'):
                                days = int(period[:-2]) * 30
                            elif period.endswith('y'):
                                days = int(period[:-1]) * 365

                            result = await provider.get_historical_data(symbol, days=days)
                        except TypeError:
                            # Try with just symbol
                            result = await provider.get_historical_data(symbol)
                else:
                    last_error = f"Provider {provider_name} has no historical data method"
                    logger.warning(last_error)
                    continue
                    
                if "error" not in result:
                    logger.debug(f"Successfully fetched history for {symbol} from {provider_name}")
                    return result
                else:
                    last_error = result["error"]
                    logger.warning(f"Provider {provider_name} failed history for {symbol}: {last_error}")
            except Exception as e:
                last_error = str(e)
                logger.error(f"Provider {provider_name} history error for {symbol}: {e}")
        
        # All providers failed
        error_msg = f"All data providers failed for historical data of {symbol}"
        if last_error:
            error_msg += f". Last error: {last_error}"
        
        logger.error(error_msg)
        return {"error": error_msg}

    async def get_historical_data(self, symbol: str, start_date=None, end_date=None, days: int = 30, interval: str = "1d", preferred_provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Backward-compatible wrapper for historical data. Accepts start_date/end_date or days and delegates to get_history.

        Args:
            symbol: Stock symbol
            start_date: Optional start datetime
            end_date: Optional end datetime
            days: Number of days to fetch if start_date/end_date not provided
            interval: Data interval string (e.g., '1d')
            preferred_provider: Preferred provider name (optional)

        Returns:
            Dictionary with historical data or error information
        """
        # Determine period string from days or start/end
        try:
            if start_date and end_date:
                # Compute days difference and use days as period (simple approach)
                delta = (end_date - start_date).days if hasattr(end_date, '__sub__') else days
                period = f"{max(1, int(delta))}d"
            else:
                period = f"{max(1, int(days))}d"

            return await self.get_history(symbol, period=period, interval=interval, preferred_provider=preferred_provider)
        except Exception as e:
            logger.error(f"get_historical_data wrapper failed for {symbol}: {e}")
            return {"error": str(e)}
    
    def _get_providers_to_try(self, preferred_provider: Optional[str] = None) -> List[str]:
        """
        Get the list of providers to try in order.
        
        Args:
            preferred_provider: Specific provider to try first (optional)
            
        Returns:
            List of provider names in try order
        """
        if preferred_provider and preferred_provider in self.providers:
            # Try preferred provider first, then others in order
            other_providers = [p for p in self.provider_order if p != preferred_provider and p in self.providers]
            return [preferred_provider] + other_providers
        else:
            # Use configured order
            return [p for p in self.provider_order if p in self.providers]

# Create singleton instance for easy access
data_provider_aggregator = DataProviderAggregator()