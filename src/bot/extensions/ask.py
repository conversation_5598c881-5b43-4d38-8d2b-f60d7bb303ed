"""
Ask Command - Intelligent Multi-Step Analysis Trading Assistant
Powered by the enhanced pipeline with AI planning and intelligent ticker discovery
"""

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import logging
import time
from typing import Optional

from src.shared.error_handling.logging import get_logger, generate_correlation_id
from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline, format_response_for_discord
from src.bot.pipeline.commands.ask.audit import create_audit_logger, get_audit_logger

logger = get_logger(__name__)

class AskCommand(commands.Cog):
    """Ask command powered by the new proactive pipeline"""

    def __init__(self, bot):
        self.bot = bot
        logger.info("✅ Ask Command initialized with proactive pipeline")
    
    @app_commands.command(name="ask", description="Ask the AI about trading and markets")
    @app_commands.describe(query="Your question about trading, stocks, or markets")
    async def ask_command(self, interaction: discord.Interaction, query: str):
        """Ask command with proactive, data-driven responses and comprehensive audit logging"""

        correlation_id = generate_correlation_id()

        # Enhanced logging for Discord interaction
        logger.info("🎯 ASK COMMAND RECEIVED", extra={
            'correlation_id': correlation_id,
            'user_id': str(interaction.user.id),
            'username': interaction.user.display_name,
            'guild_id': str(interaction.guild_id) if interaction.guild_id else None,
            'query': query,
            'query_length': len(query),
            'interaction_type': 'slash_command',
            'timestamp': time.time()
        })

        # Create audit logger for this command execution
        audit_logger = create_audit_logger(
            correlation_id=correlation_id,
            user_id=str(interaction.user.id),
            query=query
        )
        
        try:
            # AUDIT: Log command start
            audit_logger.start_step(
                step_id="ask_command_start",
                step_name="Ask Command Initialization",
                stage="discord_interaction",
                input_data={
                    "query": query,
                    "user_id": str(interaction.user.id),
                    "username": interaction.user.display_name,
                    "guild_id": str(interaction.guild_id) if interaction.guild_id else None,
                    "interaction_type": "slash_command"
                }
            )
            
            # AUDIT: Check interaction state
            interaction_responded = interaction.response.is_done()
            audit_logger.log_decision(
                decision_point="interaction_handling_strategy",
                options=["defer_response", "use_followup"],
                chosen_option="use_followup" if interaction_responded else "defer_response",
                reasoning="Interaction already responded to" if interaction_responded else "Fresh interaction, can defer",
                confidence=1.0,
                context={"interaction_responded": interaction_responded}
            )
            
            if interaction_responded:
                logger.warning(f"Interaction already responded to, using followup", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id)
                })
                audit_logger.complete_step(
                    status="completed",
                    output_data={"strategy": "followup"},
                    reasoning="Using followup for already responded interaction"
                )
                # Use followup for already responded interactions
                await self._handle_ask_followup(interaction, query, correlation_id, audit_logger)
                return

            # AUDIT: Defer interaction
            audit_logger.start_step(
                step_id="defer_interaction",
                step_name="Defer Discord Interaction",
                stage="discord_interaction",
                input_data={"interaction_id": str(interaction.id)}
            )
            
            # Defer response immediately to prevent timeout
            try:
                await interaction.response.defer(thinking=True)
                audit_logger.log_decision(
                    decision_point="defer_success",
                    options=["success", "failure"],
                    chosen_option="success",
                    reasoning="Successfully deferred interaction to prevent timeout",
                    confidence=1.0
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={"deferred": True, "thinking": True},
                    reasoning="Interaction deferred successfully"
                )
                logger.info(f"✅ Successfully deferred interaction", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id)
                })
            except Exception as defer_error:
                audit_logger.log_error(defer_error, {"stage": "defer_interaction"})
                audit_logger.log_decision(
                    decision_point="defer_failure_fallback",
                    options=["send_immediate_response", "abort"],
                    chosen_option="send_immediate_response",
                    reasoning="Defer failed, trying immediate response as fallback",
                    confidence=0.8
                )
                logger.error(f"❌ Failed to defer interaction: {defer_error}", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id)
                })
                # Try to send immediate response if defer fails
                try:
                    await interaction.response.send_message(
                        "🔄 Processing your request... Please wait a moment.",
                        ephemeral=True
                    )
                    audit_logger.complete_step(
                        status="completed",
                        output_data={"deferred": False, "immediate_response": True},
                        reasoning="Used immediate response fallback after defer failure"
                    )
                except Exception as send_error:
                    audit_logger.log_error(send_error, {"stage": "immediate_response_fallback"})
                    audit_logger.complete_step(
                        status="failed",
                        output_data={"error": "Both defer and immediate response failed"},
                        reasoning="Complete failure to respond to interaction"
                    )
                    logger.error(f"❌ Failed to send immediate response: {send_error}", extra={
                        'correlation_id': correlation_id,
                        'user_id': str(interaction.user.id)
                    })
                    return

            # AUDIT: Log command received
            audit_logger.start_step(
                step_id="command_processing",
                step_name="Process Ask Command",
                stage="command_processing",
                input_data={
                    "query": query,
                    "query_length": len(query),
                    "username": interaction.user.display_name
                }
            )
            
            logger.info(f"🚀 Ask command received", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id),
                'username': interaction.user.display_name,
                'query_length': len(query)
            })

            # AUDIT: Execute pipeline with detailed tracking
            audit_logger.log_plan(
                plan_name="Execute Ask Pipeline",
                strategy="Execute pipeline with 25s timeout and comprehensive error handling",
                steps=[
                    "execute_ask_pipeline",
                    "format_response_for_discord", 
                    "send_discord_response"
                ],
                expected_duration=15.0,
                fallback_strategy="Send timeout message if pipeline exceeds 25s",
                success_criteria=[
                    "Pipeline completes successfully",
                    "Response is formatted correctly",
                    "Response is sent to Discord"
                ]
            )

            # Execute Ask pipeline with timeout
            try:
                logger.info("🚀 STARTING ASK PIPELINE EXECUTION", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id),
                    'query': query,
                    'stage': 'pipeline_start'
                })

                result = await asyncio.wait_for(
                    execute_ask_pipeline(
                        query=query,
                        user_id=str(interaction.user.id),
                        username=interaction.user.display_name,
                        guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                        correlation_id=correlation_id,
                        interaction=interaction
                    ),
                    timeout=25.0  # 25 second timeout
                )

                logger.info("✅ ASK PIPELINE EXECUTION COMPLETED", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id),
                    'execution_time': result.execution_time if hasattr(result, 'execution_time') else 'unknown',
                    'success': result.success if hasattr(result, 'success') else 'unknown',
                    'intent': result.intent if hasattr(result, 'intent') else 'unknown',
                    'stage': 'pipeline_complete'
                })
                
                audit_logger.log_decision(
                    decision_point="pipeline_execution_success",
                    options=["success", "timeout", "error"],
                    chosen_option="success",
                    reasoning="Pipeline executed successfully within timeout",
                    confidence=1.0,
                    context={
                        "execution_time": result.execution_time,
                        "success": result.success,
                        "intent": result.intent
                    }
                )
                
                audit_logger.update_step_data({
                    "pipeline_result": {
                        "success": result.success,
                        "execution_time": result.execution_time,
                        "intent": result.intent,
                        "tools_used": result.tools_used,
                        "cache_hit": result.cache_hit
                    }
                }, "output")
                
            except asyncio.TimeoutError:
                audit_logger.log_error(
                    Exception("Pipeline execution timeout"),
                    {"timeout_seconds": 25.0, "stage": "pipeline_execution"}
                )
                audit_logger.log_decision(
                    decision_point="timeout_handling",
                    options=["send_timeout_message", "retry", "abort"],
                    chosen_option="send_timeout_message",
                    reasoning="Pipeline exceeded 25s timeout, sending user-friendly timeout message",
                    confidence=1.0
                )
                logger.warning(f"Ask pipeline timed out", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id)
                })
                await interaction.followup.send(
                    "⏰ Your request is taking longer than expected. "
                    "Please try a simpler question or try again later.",
                    ephemeral=True
                )
                audit_logger.complete_step(
                    status="failed",
                    output_data={"timeout": True, "message_sent": True},
                    reasoning="Pipeline timed out, sent timeout message to user"
                )
                return

            # AUDIT: Format response
            audit_logger.start_step(
                step_id="format_response",
                step_name="Format Response for Discord",
                stage="response_formatting",
                input_data={"result": result.__dict__ if hasattr(result, '__dict__') else str(result)}
            )
            
            # Format response for Discord
            logger.info("📝 FORMATTING RESPONSE FOR DISCORD", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id),
                'response_length': len(str(result)) if hasattr(result, '__str__') else 0,
                'stage': 'discord_formatting'
            })

            formatted_response = format_response_for_discord(result)
            
            response_type = "embed" if isinstance(formatted_response, discord.Embed) else "text"
            audit_logger.log_decision(
                decision_point="response_format_type",
                options=["embed", "text"],
                chosen_option=response_type,
                reasoning=f"Response is {response_type} based on result structure",
                confidence=1.0,
                context={"response_type": response_type}
            )
            
            audit_logger.complete_step(
                status="completed",
                output_data={
                    "response_type": response_type,
                    "formatted": True
                },
                reasoning=f"Response formatted as {response_type}"
            )

            # AUDIT: Send response
            audit_logger.start_step(
                step_id="send_response",
                step_name="Send Response to Discord",
                stage="discord_response",
                input_data={
                    "response_type": response_type,
                    "response_length": len(str(formatted_response)) if hasattr(formatted_response, '__len__') else 0
                }
            )

            # Send response
            logger.info("📤 SENDING DISCORD RESPONSE", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id),
                'response_type': response_type,
                'formatted_length': len(str(formatted_response)),
                'stage': 'discord_send'
            })

            if isinstance(formatted_response, discord.Embed):
                await interaction.followup.send(embed=formatted_response)
                audit_logger.log_decision(
                    decision_point="embed_response_sent",
                    options=["success", "failure"],
                    chosen_option="success",
                    reasoning="Embed response sent successfully",
                    confidence=1.0
                )
                logger.info(f"✅ Ask embed response sent", extra={
                    'correlation_id': correlation_id,
                    'response_type': 'embed',
                    'execution_time': result.execution_time,
                    'intent': result.intent
                })
            else:
                # Ensure text response fits Discord limits
                original_length = len(formatted_response)
                if len(formatted_response) > 2000:
                    formatted_response = formatted_response[:1997] + "..."
                    audit_logger.log_decision(
                        decision_point="text_response_truncation",
                        options=["truncate", "split", "error"],
                        chosen_option="truncate",
                        reasoning=f"Response too long ({original_length} chars), truncating to fit Discord limit",
                        confidence=1.0,
                        context={"original_length": original_length, "truncated_length": len(formatted_response)}
                    )

                await interaction.followup.send(formatted_response)
                audit_logger.log_decision(
                    decision_point="text_response_sent",
                    options=["success", "failure"],
                    chosen_option="success",
                    reasoning="Text response sent successfully",
                    confidence=1.0
                )
                logger.info(f"✅ Ask text response sent", extra={
                    'correlation_id': correlation_id,
                    'response_type': 'text',
                    'execution_time': result.execution_time,
                    'intent': result.intent,
                    'response_length': len(formatted_response)
                })
            
            audit_logger.complete_step(
                status="completed",
                output_data={
                    "response_sent": True,
                    "response_type": response_type,
                    "execution_time": result.execution_time
                },
                reasoning="Response successfully sent to Discord"
            )

        except Exception as e:
            audit_logger.log_error(e, {"stage": "ask_command", "user_id": str(interaction.user.id)})
            logger.error(f"❌ Error in Ask command: {e}", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id),
                'error_type': type(e).__name__
            }, exc_info=True)

            # Provide helpful error message
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "⚠️ I'm experiencing technical difficulties right now. "
                        "Please try rephrasing your question or try again in a moment.",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        "⚠️ I'm experiencing technical difficulties right now. "
                        "Please try rephrasing your question or try again in a moment.",
                        ephemeral=True
                    )
                audit_logger.log_decision(
                    decision_point="error_response_sent",
                    options=["success", "failure"],
                    chosen_option="success",
                    reasoning="Error response sent to user",
                    confidence=1.0
                )
            except Exception as send_error:
                audit_logger.log_error(send_error, {"stage": "error_response"})
                logger.error(f"❌ Failed to send error response: {send_error}", extra={
                    'correlation_id': correlation_id,
                    'user_id': str(interaction.user.id)
                })
        
        finally:
            # AUDIT: Complete pipeline audit
            audit_logger.log_pipeline_end(
                success=True,  # Will be updated based on actual result
                final_result={
                    "correlation_id": correlation_id,
                    "user_id": str(interaction.user.id),
                    "query": query,
                    "completed_at": time.time()
                }
            )

    async def _handle_ask_followup(self, interaction: discord.Interaction, query: str, correlation_id: str, audit_logger):
        """Handle ask command when interaction is already responded to"""
        try:
            # AUDIT: Log followup start
            audit_logger.start_step(
                step_id="ask_followup_start",
                step_name="Handle Ask Followup",
                stage="discord_followup",
                input_data={
                    "query": query,
                    "user_id": str(interaction.user.id),
                    "interaction_id": str(interaction.id)
                }
            )
            
            logger.info(f"🔄 Handling ask followup", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id)
            })
            
            # Send initial processing message
            await interaction.followup.send(
                "🔄 Processing your request... Please wait a moment.",
                ephemeral=True
            )
            
            audit_logger.log_decision(
                decision_point="followup_processing_message",
                options=["sent", "failed"],
                chosen_option="sent",
                reasoning="Sent processing message to user",
                confidence=1.0
            )
            
            # Execute pipeline
            result = await asyncio.wait_for(
                execute_ask_pipeline(
                    query=query,
                    user_id=str(interaction.user.id),
                    username=interaction.user.display_name,
                    guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                    correlation_id=correlation_id,
                    interaction=interaction
                ),
                timeout=25.0
            )
            
            audit_logger.log_decision(
                decision_point="followup_pipeline_execution",
                options=["success", "timeout", "error"],
                chosen_option="success",
                reasoning="Pipeline executed successfully in followup mode",
                confidence=1.0,
                context={
                    "execution_time": result.execution_time,
                    "success": result.success
                }
            )
            
            # Format and send response
            formatted_response = format_response_for_discord(result)
            
            response_type = "embed" if isinstance(formatted_response, discord.Embed) else "text"
            
            if isinstance(formatted_response, discord.Embed):
                await interaction.followup.send(embed=formatted_response)
            else:
                if len(formatted_response) > 2000:
                    formatted_response = formatted_response[:1997] + "..."
                await interaction.followup.send(formatted_response)
            
            audit_logger.log_decision(
                decision_point="followup_response_sent",
                options=["success", "failure"],
                chosen_option="success",
                reasoning=f"Followup response sent as {response_type}",
                confidence=1.0,
                context={"response_type": response_type}
            )
                
            logger.info(f"✅ Ask followup response sent", extra={
                'correlation_id': correlation_id,
                'response_type': 'followup'
            })
            
            audit_logger.complete_step(
                status="completed",
                output_data={
                    "response_sent": True,
                    "response_type": response_type,
                    "execution_time": result.execution_time
                },
                reasoning="Followup response successfully sent to Discord"
            )
            
        except Exception as e:
            audit_logger.log_error(e, {"stage": "ask_followup", "user_id": str(interaction.user.id)})
            logger.error(f"❌ Error in ask followup: {e}", extra={
                'correlation_id': correlation_id,
                'user_id': str(interaction.user.id)
            })
            try:
                await interaction.followup.send(
                    "⚠️ I'm experiencing technical difficulties. Please try again later.",
                    ephemeral=True
                )
                audit_logger.log_decision(
                    decision_point="followup_error_response",
                    options=["sent", "failed"],
                    chosen_option="sent",
                    reasoning="Error response sent in followup mode",
                    confidence=1.0
                )
            except:
                audit_logger.log_decision(
                    decision_point="followup_error_response",
                    options=["sent", "failed"],
                    chosen_option="failed",
                    reasoning="Failed to send error response in followup mode",
                    confidence=1.0
                )
                pass

async def setup(bot):
    """Add the ask command to the bot"""
    await bot.add_cog(AskCommand(bot))
    logger.info("✅ Ask Command extension loaded successfully")