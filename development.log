# Keeping only recent logs from 2025-09-29
2025-09-29 00:03:17,282 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:03:17,282 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:03:17,958 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 00:03:17,959 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 00:03:17,959 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 00:03:17,962 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:03:17,963 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 00:03:17,963 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 00:03:17,963 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 00:03:18,201 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 00:03:18,202 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 00:03:18,203 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 00:03:18,208 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 00:03:18,305 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 00:03:18,314 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 00:03:18,315 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 00:03:18,315 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 00:03:18,316 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 00:03:18,316 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 00:03:18,316 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 00:03:18,316 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 00:03:18,552 - src.shared.technical_analysis.signal_generator - INFO - SignalGenerator initialized
2025-09-29 00:03:18,662 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:03:18,662 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 00:03:18,662 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 00:03:18,662 - src.shared.ai_services.unified_ai_processor - INFO - ✅ Core AI engine initialized
2025-09-29 00:03:18,663 - src.shared.ai_services.unified_ai_processor - INFO - ✅ Infrastructure components initialized
2025-09-29 00:03:18,663 - src.shared.ai_services.unified_ai_processor - INFO - ✅ Specialized services initialized
2025-09-29 00:03:18,663 - src.shared.ai_services.unified_ai_processor - INFO - ✅ Unified AI Processor initialized for pipeline: unknown
2025-09-29 00:03:18,683 - src.shared.ai_services.simple_model_config - INFO - ✅ Loaded simple AI config from config/ai_models_simple.yaml
2025-09-29 00:03:19,543 - src.shared.ai_chat.ai_client - INFO - ✅ [unknown] OpenRouter client configured with model: moonshotai/kimi-k2-0905
2025-09-29 00:03:19,594 - src.database.unified_db - INFO - Supabase client initialized successfully
2025-09-29 00:03:20,066 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:20,071 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:20,231 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:20,233 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:20,707 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:20,709 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:20,945 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:20,946 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:21,079 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:21,080 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:21,248 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:21,249 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:21,390 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:21,392 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:21,615 - httpx - INFO - HTTP Request: GET https://sgxjackuhalscowqrulv.supabase.co/rest/v1/configs?select=section%2Cdata&environment=eq.development "HTTP/2 200 OK"
2025-09-29 00:03:21,619 - src.core.config_manager - INFO - Loaded 2 config sections from database for development
2025-09-29 00:03:21,739 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Registered 50 tools across 10 client types
2025-09-29 00:03:21,779 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Trading MCP client initialized with Alpha Vantage
2025-09-29 00:03:21,779 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - MCP Manager initialized with unified tool registry
2025-09-29 00:07:48,929 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:07:49,480 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 00:07:49,481 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 00:07:49,481 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 00:07:49,484 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 00:07:49,484 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 00:07:49,484 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 00:07:49,484 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 00:07:49,680 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 00:07:49,680 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 00:07:49,681 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 00:07:49,685 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 00:07:49,767 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 00:07:49,774 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 00:07:49,775 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 00:07:49,775 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 00:07:49,775 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 00:07:49,775 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 00:07:49,775 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 00:07:49,775 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 00:07:49,789 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Registered 50 tools across 10 client types
2025-09-29 00:07:49,814 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Trading MCP client initialized with Alpha Vantage
2025-09-29 00:07:49,814 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - MCP Manager initialized with unified tool registry
2025-09-29 00:07:49,815 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Registered 50 tools across 10 client types
2025-09-29 00:07:49,815 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Trading MCP client initialized with Alpha Vantage
2025-09-29 00:07:49,815 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - MCP Manager initialized with unified tool registry
2025-09-29 10:44:28,227 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 10:44:28,849 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 10:44:28,849 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 10:44:28,849 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 10:44:28,851 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 10:44:28,851 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 10:44:28,851 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 10:44:28,851 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 10:44:29,067 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 10:44:29,068 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 10:44:29,069 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 10:44:29,074 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 10:44:29,178 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 10:44:29,192 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 10:44:29,192 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 10:44:29,192 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 10:44:29,192 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 10:44:29,193 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 10:44:29,193 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 10:44:29,193 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 10:44:29,222 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Registered 50 tools across 10 client types
2025-09-29 10:44:29,240 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Trading MCP client initialized with Alpha Vantage
2025-09-29 10:44:29,240 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - MCP Manager initialized with unified tool registry
2025-09-29 10:44:29,240 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Registered 50 tools across 10 client types
2025-09-29 10:44:29,240 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - Trading MCP client initialized with Alpha Vantage
2025-09-29 10:44:29,240 - src.bot.pipeline.commands.ask.tools.mcp_manager - INFO - MCP Manager initialized with unified tool registry
2025-09-29 10:45:08,789 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 10:45:09,420 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 10:45:09,420 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 10:45:09,420 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 10:45:09,423 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 10:45:09,423 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 10:45:09,423 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 10:45:09,423 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 10:45:09,647 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 10:45:09,648 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 10:45:09,650 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 10:45:09,656 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 10:45:09,757 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 10:45:09,769 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 10:45:09,769 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 10:45:09,769 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 10:45:09,770 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 10:45:09,770 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 10:45:09,770 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 10:45:09,770 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 10:45:09,775 - src.data_providers.data_source_manager - WARNING - DataSourceManager initialized using placeholder logic.
2025-09-29 10:45:09,776 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Initializing RobustFinancialAnalyzer - checking pandas
2025-09-29 10:45:09,776 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: Pandas available in RobustFinancialAnalyzer - version: 2.3.2
2025-09-29 10:45:09,776 - src.shared.ai_services.ai_processor_robust - INFO - DEBUG: RobustFinancialAnalyzer init complete
2025-09-29 10:45:09,776 - src.shared.ai_services.ai_chat_processor - INFO - Initialized AIChatProcessorWrapper for pipeline: unknown
2025-09-29 10:45:09,776 - __main__ - INFO - 🚀 Trading MCP Server initialized
2025-09-29 12:47:40,665 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-09-29 12:47:40,668 - httpx - DEBUG - load_verify_locations cafile='/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/certifi/cacert.pem'
2025-09-29 12:47:40,703 - src.api.data.providers.alpha_vantage - WARNING - Alpha Vantage provider not configured - API key missing
2025-09-29 12:47:41,376 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 12:47:41,377 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 12:47:41,378 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 12:47:41,382 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 12:47:41,467 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 12:47:41,475 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 12:47:41,475 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 12:47:41,475 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 12:47:41,475 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 12:47:41,476 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 12:47:41,476 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 12:47:41,476 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 12:47:44,104 - mcp.server.lowlevel.server - DEBUG - Initializing server 'arxiv-mcp-server'
2025-09-29 12:47:44,105 - mcp.server.lowlevel.server - DEBUG - Registering handler for PromptListRequest
2025-09-29 12:47:44,105 - mcp.server.lowlevel.server - DEBUG - Registering handler for GetPromptRequest
2025-09-29 12:47:44,105 - mcp.server.lowlevel.server - DEBUG - Registering handler for ListToolsRequest
2025-09-29 12:47:44,105 - mcp.server.lowlevel.server - DEBUG - Registering handler for CallToolRequest
2025-09-29 12:49:58,743 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-09-29 12:49:58,745 - httpx - DEBUG - load_verify_locations cafile='/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/certifi/cacert.pem'
2025-09-29 12:49:58,778 - src.api.data.providers.alpha_vantage - WARNING - Alpha Vantage provider not configured - API key missing
2025-09-29 12:49:59,421 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 12:49:59,422 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 12:49:59,423 - src.shared.services.optimization_service - INFO - PerformanceOptimizer initialized
2025-09-29 12:49:59,426 - src.shared.services.performance_monitor - INFO - PerformanceMonitor initialized
2025-09-29 12:49:59,504 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
2025-09-29 12:49:59,511 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance
2025-09-29 12:49:59,512 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage
2025-09-29 12:49:59,512 - src.shared.data_providers.aggregator - WARNING - Provider polygon is not configured, skipping
2025-09-29 12:49:59,512 - src.shared.data_providers.finnhub_provider - INFO - Finnhub provider initialized
2025-09-29 12:49:59,512 - src.shared.data_providers.aggregator - INFO - Initialized data provider: finnhub
2025-09-29 12:49:59,512 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized
2025-09-29 12:49:59,512 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback
2025-09-29 12:50:02,225 - mcp.server.lowlevel.server - DEBUG - Initializing server 'arxiv-mcp-server'
2025-09-29 12:50:02,225 - mcp.server.lowlevel.server - DEBUG - Registering handler for PromptListRequest
2025-09-29 12:50:02,225 - mcp.server.lowlevel.server - DEBUG - Registering handler for GetPromptRequest
2025-09-29 12:50:02,225 - mcp.server.lowlevel.server - DEBUG - Registering handler for ListToolsRequest
2025-09-29 12:50:02,225 - mcp.server.lowlevel.server - DEBUG - Registering handler for CallToolRequest
2025-09-29 13:24:18,969 - src.shared.config.config_manager - INFO - ✅ Loaded config from config.yaml with environment variable substitution
2025-09-29 13:24:18,969 - src.shared.config.config_manager - INFO - ✅ Configuration loaded with 12 settings
2025-09-29 13:24:19,029 - src.shared.monitoring.observability - INFO - ✅ Observability manager initialized
